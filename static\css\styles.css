@import url('https://fonts.googleapis.com/css2?family=Noto+Sans:wght@100..900&family=Noto+Serif:wght@100..900&family=Noto+Sans+Mono:wght@100..900&family=Noto+Sans+Display:wght@100..900&display=swap');

/* font-family: 'Noto Sans', sans-serif; */
/* font-family: 'Noto Serif', serif; */

/* font-style: italic; */
/* font-style: normal; */

/* The font-stretch property accepts values like
ultra-condensed
extra-condensed
condensed
semi-condensed
normal
semi-expanded
expanded
extra-expanded
ultra-expanded
*/

/* Font Weight Values:
100: Thin
200: Extra Light
300: Light
400: Normal
500: Medium
600: Semi-Bold
700: Bold
800: Extra Bold
900: Black
*/

:root {
    font-family: 'Noto Sans', sans-serif;
    font-size: 16px;
    font-weight: 300;
    font-stretch: normal;
    font-style: normal;

    --color-disabled-background: rgba(117, 117, 117, 0.2);

    /* DARK --color-text-bright value #F0DAC6 is used in js*/
    /* --color-background-dark: #000000;
    --color-background-middle: #464B53;
    --color-background-bright: #2d2d2d;
    --color-text-title: #FD6630;
    --color-text-subtitle: #FEBA9C;
    --color-text-black: #F0DAC6;
    --color-text-dark: #FEBA9C;
    --color-text-middle: #FF6631;
    --color-text-bright: #F0DAC6;
    --color-text-brighter: #fdf1e6;
    --color-border-middle: rgb(0, 0, 0);
    --color-input-lines: #FEBA9C;
    --color-hr-lines: #6b4400;
    --color-selected-green: hsl(55, 96%, 38%);
    --color-selected-red: #ff6161;
    --color-disabled: #6f6f6f;
    --color-list-background: rgba(250, 167, 0, 0.07);
    --color-error-title: #e20000;
    --padding-border-middle: 0px;
    --width-border-middle: 5px;
    --radius-border-middle: 10px; */


    /* ORANGE */
    /* --color-background-dark: #472c07;
    --color-background-middle: #d89c52;
    --color-background-bright: #f6e1c5;
    --color-text-title: #000000;
    --color-text-subtitle: #000000;
    --color-text-black: #000000;
    --color-text-dark: #8b4f06;
    --color-text-middle: #945200;
    --color-text-bright: rgb(222, 203, 177);
    --color-text-brighter: #fff6eb;
    --color-border-middle: #e5ad63;
    --color-input-lines: #d89c52;
    --color-hr-lines: #6b4400;
    --color-selected-green: hsl(55, 96%, 38%);
    --color-selected-red: #bc2600;
    --color-disabled: #6f6f6f;
    --color-list-background: rgba(250, 167, 0, 0.07);
    --color-error-title: #ff3700;
    --padding-border-middle: 4px;
    --width-border-middle: 4px;
    --radius-border-middle: 12px; */

    /* BROWN */
    /* --color-background-dark: #4a3f2c;
    --color-background-middle: #b19c77;
    --color-background-bright: #e9e3d8;
    --color-text-title: #000000;
    --color-text-subtitle: #000000;
    --color-text-black: #000000;
    --color-text-dark: #6b4400;
    --color-text-middle: #b19c77;
    --color-text-bright: #e4dac7;
    --color-text-brighter: #fbf9f6;
    --color-border-middle: #c6b18e;
    --color-input-lines: #6b4400;
    --color-hr-lines: #6b4400;
    --color-selected-green: hsl(55, 96%, 38%);
    --color-selected-red: #ff3300;
    --color-disabled: #6f6f6f;
    --color-list-background: rgba(250, 167, 0, 0.07);
    --color-error-title: #a02300;
    --padding-border-middle: 4px;
    --width-border-middle: 4px;
    --radius-border-middle: 12px; */

    /* PURPLE */
    --color-background-dark: #663b18;
    --color-background-middle: #e0dbcd;
    --color-background-bright: #f6f5ee;
    --color-text-on-background-middle: #7B3800;
    --color-text-title: #000000;
    --color-text-subtitle: #000000;
    --color-text-black: #000000;
    --color-text-dark: #7c3800;
    --color-text-middle: #615637;
    --color-text-bright: #f6e1c5;
    --color-text-brighter: #fbf2e6;
    --color-border-middle: rgba(102, 59, 24, 0.2);
    --color-input-lines: #994500;
    --color-hr-lines: #6b4400;
    --color-selected-green: hsl(55, 96%, 38%);
    --color-selected-red: #ff3300;
    --color-disabled: #6f6f6f;
    --color-list-background: rgba(250, 167, 0, 0.07);
    --color-error-title: #bc2600;
    --padding-border-middle: 0px;
    --width-border-middle: 5px;
    --radius-border-middle: 10px;
    --color-hamburger-hover: #2f450e;
    }

html, body {
    font-family: 'Noto Sans', sans-serif;
    font-weight: 300;
    font-stretch: normal;
    font-style: normal;
    font-size: 1.125rem;
    background-color: var(--color-background-middle);
    color: var(--color-text-black);
    height: 100%;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active{
    font-family: 'Noto Sans', sans-serif;
    font-weight: 400;
    font-stretch: normal;
    font-style: normal;
    font-size: 18px;
    -webkit-background-clip: text;
    -webkit-text-fill-color: var(--color-text-black);
    transition: background-color 5000s ease-in-out 0s;
    box-shadow: inset 0 0 20px 20px rgba(35, 35, 35, 0);
}

.Site {
    display: flex;
    min-height: 100vh;
    flex-direction: column;
}

.container {
    width: clamp(98%, 92%, 1200px);
    margin: auto;
    height: 100%;
}

hr.footer-hr {
    display: block;
    height: 1px;
    border: 0;
    border-top: 1px solid #ccc;
    margin: 0 4rem;
    padding: 0;
    border-color: var(--color-text-dark);
}

a {
    color: var(--color-text-dark);
}

.fa-square-facebook{
    font-size: 1.375rem;
}
.fa-linkedin{
    font-size: 1.375rem;
}
.fa-envelope{
    font-size: 1.5625rem;
}

.navbarlogo {
    padding-right: 0.625rem;
    height: 4.375rem;
}

.footerlogo {
    padding-right: 0.625rem;
    width: 1.5rem;
    height: 1.5rem;
}

.content-div{
    width: auto;
    max-width: 51.25rem;
    margin: 3.75rem auto 3rem auto;
    border-style: solid;
    border-color: var(--color-border-middle);
    border-width: var(--width-border-middle);
    border-radius: var(--radius-border-middle);
    padding: var(--padding-border-middle);
}

.content-div.slidetransition {
  view-transition-name: slide-it;
}

@keyframes fadeIn {
    to {
    opacity: 1;
    }
}

@view-transition {
    navigation: auto;
}

/* Disable view transitions on mobile Safari to fix form error rendering bugs */
@supports (-webkit-touch-callout: none) {
    @media (max-width: 768px) {
        .content-div.slidetransition {
            view-transition-name: none !important;
        }

        @view-transition {
            navigation: none;
        }
    }
}

@keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fade-out {
    to { opacity: 0; }
}

@keyframes slide-from-right {
    from { transform: translateX(90px); }
}

@keyframes slide-to-left {
    to { transform: translateX(-90px); }
}

::view-transition-old(slide-it) {
    animation:
    180ms cubic-bezier(0.4, 0, 1, 1) both fade-out,
    600ms cubic-bezier(0.4, 0, 0.2, 1) both slide-to-left;
}
::view-transition-new(slide-it) {
    animation:
    420ms cubic-bezier(0, 0, 0.2, 1) 90ms both fade-in,
    600ms cubic-bezier(.63,.7,.16,1.28) both slide-from-right;
}

::view-transition-old(simple-fade-slide) {
    animation: 300ms ease-out both fade-out;
}
::view-transition-new(simple-fade-slide) {
    animation:
        300ms ease-out both fade-in,
        600ms ease-out both slide-from-above;
}

@keyframes slide-from-above {
    from { transform: translateY(-30px); }
    to { transform: translateY(0); }
}

#errordiv {
  view-transition-name: simple-fade-slide;
}

[popover] {
  transition: opacity 0.2s, transform 0.2s, display 0.2s allow-discrete;
  opacity: 0;
  transform: translateY(48px);
  &:popover-open {
    opacity: 1;
    transform: none;
    @starting-style {
      & {
        opacity: 0;
        transform: translateY(-16px);
      }
    }
  }
}


input#menu {
    display: none;
}

.icon {
    width: 2.25rem;
    height: 2.25rem;
    position: relative;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: translateY(-0.0625rem);
}

.menu {
    width: 1.75rem;
    height: 0.125rem;
    background-color: var(--color-text-on-background-middle);
    position: relative;
    transition: all 0.3s ease;
}

.menu::before,
.menu::after {
    content: '';
    position: absolute;
    width: 28px;
    height: 2px;
    background-color: var(--color-text-on-background-middle);
    left: 0;
    transition: all 0.3s ease;
}

.menu::before {
    top: -9px;
}

.menu::after {
    top: 9px;
}

#menu:checked + .icon .menu {
    background: transparent;
}

#menu:checked + .icon .menu::before {
    transform: rotate(45deg);
    top: 0;
}

#menu:checked + .icon .menu::after {
    transform: rotate(-45deg);
    top: 0;
}

.icon:hover .menu,
.icon:hover .menu::before,
.icon:hover .menu::after {
    background-color: var(--color-hamburger-hover);
}




.dropdown {
    position: absolute;
    top: 2.5rem;
    right: 0;
    width: auto;
    min-width: 9.375rem;
    background-color: var(--color-background-dark);
    border: 0.25rem solid var(--color-border-middle);
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.1);
    overflow: hidden;
    max-height: 0;
    opacity: 0;
    transform: translateY(-0.625rem);
    transition: max-height 0.3s ease, opacity 0.3s ease, transform 0.4s ease;
    display: inline-block;
    white-space: nowrap;
    z-index: 1001;
}

#menu:checked ~ .dropdown {
    max-height: 31.25rem;
    opacity: 1;
    transform: translateY(0);
}

.dropdown-item {
    padding: 0.4375rem 1.5625rem 0.4375rem 0.625rem;
    cursor: pointer;
    transition: opacity 0.3s ease, transform 0.3s ease;
    opacity: 0;
    transform: translateY(-0.625rem);
    display: flex;
    align-items: center;
    border-bottom: 0.0625rem solid var(--color-background-middle);
}

.dropdown-item .icon i {
    display: inline-block;
}

.dropdown-item .icon {
    display: inline-flex;
    align-items: center;
    margin-right: 0.75rem;
    font-size: 16px;
    color: var(--color-text-bright);
}

.dropdown-item .text {
    color: var(--color-text-bright);
    display: flex;
    align-items: center;
}

#menu:checked ~ .dropdown .dropdown-item {
    opacity: 1;
    transform: translateY(0);
}

#menu:checked ~ .dropdown .dropdown-item:nth-child(1) {
    transition-delay: 0.01s;
}
#menu:checked ~ .dropdown .dropdown-item:nth-child(2) {
    transition-delay: 0.05s;
}
#menu:checked ~ .dropdown .dropdown-item:nth-child(3) {
    transition-delay: 0.1s;
}
#menu:checked ~ .dropdown .dropdown-item:nth-child(4) {
    transition-delay: 0.15s;
}
.dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-item:hover {
    background-color: var(--color-background-bright);
}
.dropdown-item:hover .text {
    color: var(--color-text-dark);
}
.dropdown-item:hover .icon {
    color: var(--color-text-dark);
}

/* Mobile Responsive Media Queries */
@media (max-width: 768px) {
    .container {
        width: 95%;
        padding: 0 0.5rem;
    }

    .content-div {
        margin: 1.5rem 1rem;
        max-width: calc(100% - 2rem);
    }

    /* Simple mobile padding */
    .content-div > div {
        padding-left: 2rem !important;
        padding-right: 2rem !important;
    }

    /* Footer spacing on mobile */
    .content-div {
        margin-bottom: 2rem !important;
    }

    hr.footer-hr {
        margin: 0 2rem;
    }



    .navbarlogo {
        height: 4rem;
    }

    /* Improve touch targets for mobile */
    .icon {
        width: 2.75rem;
        height: 2.75rem;
    }

    /* Better mobile spacing */
    body {
        font-size: 1rem;
    }

    /* Mobile dropdown improvements */
    .dropdown-item {
        padding: 0.625rem 1.5625rem 0.625rem 0.75rem;
    }

    .dropdown-item .icon {
        font-size: 18px;
        margin-right: 0.875rem;
    }

    .dropdown-item .text {
        font-size: 17px;
    }
}

@media (max-width: 480px) {
    .container {
        width: 98%;
        padding: 0 0.25rem;
    }

    .content-div {
        margin: 1rem 0.5rem;
        max-width: calc(100% - 1rem);
    }

    /* Simple small mobile padding */
    .content-div > div {
        padding-left: 1.0rem !important;
        padding-right: 1.0rem !important;
    }

    /* Footer spacing on small mobile */
    .content-div {
        margin-bottom: 1.5rem !important;
    }

    hr.footer-hr {
        margin: 0 2rem;
    }



    .navbarlogo {
        height: 3.5rem;
    }

    /* Enhanced dropdown for small mobile (iPhone) */
    .dropdown-item {
        padding: 0.75rem 1.5625rem 0.75rem 0.875rem;
        min-height: 2.5rem; /* Better touch target but not too tall */
    }

    .dropdown-item .icon {
        font-size: 20px;
        margin-right: 1rem;
        min-width: 1.25rem; /* Ensure consistent icon spacing */
    }

    .dropdown-item .text {
        font-size: 18px;
        font-weight: 400; /* Slightly bolder for better readability */
    }

    /* Slightly wider dropdown on small screens */
    .dropdown {
        min-width: 10rem;
    }
}
